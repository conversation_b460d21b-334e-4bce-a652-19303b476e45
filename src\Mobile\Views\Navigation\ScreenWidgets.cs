﻿using AppoMobi.Xam;

namespace AppoMobi.Mobile.Views
{
    public class ScreenWidgets : AppScreen
    {
        public ScreenWidgets()
        {
            var widgets = TabsAndMenu.TabsList.ToList();
            var menu = TabsAndMenu.MenuList.ToList();

            VerticalOptions = LayoutOptions.Fill;

#if WINDOWS
            Background =
                new LinearGradientBrush(
                    new GradientStopCollection()
                    {
                        new GradientStop(BackColors.GradientPageFaderStart, 0),
                        new GradientStop(BackColors.GradientPageFaderStartEnd, 1),
                    }, new(0, 0), new(0, 1));
#endif


            Children = new List<SkiaControl>()
            {
#if WINDOWS
                new SkiaImage() { UseCache = SkiaCacheType.Image,
                        Opacity = 0.15,
                        Source = @"Images\back.jpg", }
                    .WithRowSpan(2)
                    .Fill(),
#endif

                new SkiaStack()
                {
                    Children = new List<SkiaControl>()
                    {
                        //navbar with padding
                        new TopNavBar(),

                        //widgets
                        new SkiaScroll()
                        {
                            RespondsToGestures = false,
                            Content = new SkiaStack()
                            {
                                Spacing = 16,
                                UseCache = SkiaCacheType.ImageComposite,
                                Padding = new(0, 0,0,16),
                                Children = new List<SkiaControl>()
                                {
                                    new SkiaWrap()
                                    {
                                        HorizontalOptions = LayoutOptions.Center,
                                        UseCache = SkiaCacheType.ImageComposite,
                                        ItemsSource = widgets,
                                        Spacing = 8,
                                        Split = 0,
                                        ItemTemplateType = typeof(ModuleWidget)
                                    },
                                    new SkiaWrap()
                                    {
                                        HorizontalOptions = LayoutOptions.Center,
                                        UseCache = SkiaCacheType.ImageComposite,
                                        ItemsSource = menu,
                                        Spacing = 8,
                                        Split = 0,
                                        ItemTemplateType = typeof(ModuleWidgetMenu)
                                    }
                                }
                            }
                        }.Fill()

                        //bottom padding
                    }
                }
            };


        }
    }
}
