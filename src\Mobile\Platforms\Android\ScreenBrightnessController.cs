using Android.App;
using Android.Content;
using Android.Provider;
using Microsoft.Maui.ApplicationModel;

namespace AppoMobi.Helpers;

public partial class ScreenBrightnessController : IBrightnessService
{
    private float _originalBrightness;
    private bool _hasSetBrightness = false;
    private Activity _activity;

    public ScreenBrightnessController()
    {
        _activity = Platform.CurrentActivity;
        if (_activity != null)
        {
            _originalBrightness = GetCurrentBrightness();
        }
    }

    public Task<bool> SetBrightnessAsync(float brightness)
    {
        try
        {
            if (_activity == null)
            {
                System.Diagnostics.Debug.WriteLine("Activity is null, cannot set brightness");
                return Task.FromResult(false);
            }

            if (!_hasSetBrightness)
            {
                _originalBrightness = GetCurrentBrightness();
                _hasSetBrightness = true;
            }

            // Clamp brightness value between 0.0 and 1.0
            brightness = Math.Clamp(brightness, 0.0f, 1.0f);

            // Set brightness for the current window
            var window = _activity.Window;
            if (window != null)
            {
                var layoutParams = window.Attributes;
                layoutParams.ScreenBrightness = brightness;
                window.Attributes = layoutParams;
                
                System.Diagnostics.Debug.WriteLine($"Set screen brightness to: {brightness}");
                return Task.FromResult(true);
            }

            return Task.FromResult(false);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to set brightness: {ex.Message}");
            return Task.FromResult(false);
        }
    }

    public Task<bool> RestoreBrightnessAsync()
    {
        try
        {
            if (_activity == null)
            {
                System.Diagnostics.Debug.WriteLine("Activity is null, cannot restore brightness");
                return Task.FromResult(false);
            }

            if (_hasSetBrightness)
            {
                var window = _activity.Window;
                if (window != null)
                {
                    var layoutParams = window.Attributes;
                    // Set to -1 to use system brightness
                    layoutParams.ScreenBrightness = -1.0f;
                    window.Attributes = layoutParams;
                    
                    _hasSetBrightness = false;
                    System.Diagnostics.Debug.WriteLine("Restored screen brightness to system default");
                }
            }

            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to restore brightness: {ex.Message}");
            return Task.FromResult(false);
        }
    }

    private float GetCurrentBrightness()
    {
        try
        {
            if (_activity == null)
                return 0.5f; // Default fallback

            var window = _activity.Window;
            if (window?.Attributes != null)
            {
                var currentBrightness = window.Attributes.ScreenBrightness;
                
                // If brightness is -1, it means using system brightness
                if (currentBrightness < 0)
                {
                    // Try to get system brightness
                    try
                    {
                        var systemBrightness = Settings.System.GetInt(
                            _activity.ContentResolver,
                            Settings.System.ScreenBrightness);
                        
                        // Convert from 0-255 range to 0.0-1.0 range
                        return systemBrightness / 255.0f;
                    }
                    catch
                    {
                        return 0.5f; // Default fallback
                    }
                }
                
                return currentBrightness;
            }

            return 0.5f; // Default fallback
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to get current brightness: {ex.Message}");
            return 0.5f; // Default fallback
        }
    }
}
