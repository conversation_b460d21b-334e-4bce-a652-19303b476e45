﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AppoMobi.Main;
using AppoMobi.Xam;

namespace AppoMobi.Helpers
{
    public partial class NativeTasks : INativeTasks
    {

    }

    public interface IBrightnessService
    {
        /// <summary>
        /// Sets the screen brightness to a value between 0.0 (darkest) and 1.0 (brightest)
        /// </summary>
        /// <param name="brightness">Brightness value between 0.0 and 1.0</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> SetBrightnessAsync(float brightness);

        /// <summary>
        /// Restores the screen brightness to the system default or previous value
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> RestoreBrightnessAsync();
    }

    public partial class ScreenBrightnessController : IBrightnessService
    {
#if !WINDOWS && !IOS && !ANDROID
        public Task<bool> SetBrightnessAsync(float brightness)
        {
            // Not supported on this platform
            return Task.FromResult(false);
        }

        public Task<bool> RestoreBrightnessAsync()
        {
            // Not supported on this platform
            return Task.FromResult(false);
        }
#endif
    }

}
