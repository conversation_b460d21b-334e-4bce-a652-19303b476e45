﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" android:installLocation="auto" package="com.appomobi.artoffoto" android:versionName="9.00" android:versionCode="9000001">
	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<permission android:name="com.appomobi.artoffoto.permission.C2D_MESSAGE" android:protectionLevel="signature" />
	<uses-permission android:name="com.appomobi.artoffoto.permission.C2D_MESSAGE" />
	<uses-permission android:name="android.permission.CAMERA" />
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
  <uses-permission android:name="android.permission.VIBRATE" />
    <!--geotag photos-->
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
	<application android:label="Art of Foto" android:icon="@mipmap/ic_launcher" android:requestLegacyExternalStorage="true">
		<provider android:name="androidx.core.content.FileProvider" android:authorities="com.appomobi.artoffoto.fileprovider" android:exported="false" android:grantUriPermissions="true">
			<meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/file_paths"></meta-data>
		</provider>
	</application>
</manifest>